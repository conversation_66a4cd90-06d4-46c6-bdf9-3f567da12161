#!/usr/bin/env python3
"""
Logging Utilities for Magnetic-One Test Script (M1)
===================================================

This module contains utility functions for LLM logging setup and finalization.
"""

import logging
from pathlib import Path


def setup_llm_logging(scenario_id: str, endpoint_type: str, model: str, output_dir: Path, run_timestamp: str = None) -> Path:
    """
    设置LLM调用的详细日志记录，保存所有输入给LLM的消息和token统计
    每次调用会初始化一个新的日志文件，确保每次任务的日志独立保存

    Args:
        scenario_id: 场景ID (如 "1")
        endpoint_type: 端点类型 (如 "ollama", "openai")
        model: 模型名称 (如 "llama3.1")
        output_dir: 输出目录路径
        run_timestamp: 运行时间戳 (可选)

    Returns:
        Path: LLM日志文件的路径
    """
    from datetime import datetime, timezone

    # 确保输出目录存在
    output_dir.mkdir(parents=True, exist_ok=True)

    # 创建LLM调用日志文件路径，与现有日志命名保持一致
    if run_timestamp:
        llm_log_path = output_dir / f"scenario_{scenario_id}_{endpoint_type}_{model}_{run_timestamp}_llm_calls.log"
    else:
        llm_log_path = output_dir / f"scenario_{scenario_id}_{endpoint_type}_{model}_llm_calls.log"

    # 初始化日志文件 - 使用UTC时间
    start_time = datetime.now(timezone.utc)
    start_msg = f"=== Started LLM logging for scenario {scenario_id} with {endpoint_type} at {start_time.strftime('%H:%M:%S')} UTC ===\n"
    start_msg += f"=== Model: {model} ===\n"
    start_msg += f"=== This log contains all LLM calls with complete input messages and token statistics ===\n\n"

    # 写入初始化信息（覆盖模式，确保每次任务都是新的日志）
    llm_log_path.write_text(start_msg, encoding="utf-8")

    # 配置日志 - 捕获autogen_core的事件日志
    import logging
    from autogen_core import EVENT_LOGGER_NAME

    # 获取autogen_core的事件logger
    event_logger = logging.getLogger(EVENT_LOGGER_NAME)
    event_logger.setLevel(logging.INFO)

    # 清除现有的handlers（避免重复）
    for handler in event_logger.handlers[:]:
        if isinstance(handler, logging.FileHandler) and str(llm_log_path) in str(handler.baseFilename):
            event_logger.removeHandler(handler)

    # 创建文件handler，追加模式
    file_handler = logging.FileHandler(llm_log_path, mode='a', encoding='utf-8')
    file_handler.setLevel(logging.INFO)

    # 设置简单格式 - 只记录消息内容，不添加时间戳
    formatter = logging.Formatter('%(message)s')
    file_handler.setFormatter(formatter)

    # 添加handler到event logger
    event_logger.addHandler(file_handler)

    # 确保日志传播到root logger（这样可以被捕获）
    event_logger.propagate = True

    print(f"📝 LLM调用日志已初始化: {llm_log_path}")
    print(f"✓ 将记录所有输入给LLM的完整消息内容和token使用统计")
    print(f"✓ 每次运行都会创建新的日志文件，避免多次调试混淆")
    return llm_log_path


def finalize_llm_logging(llm_log_path: Path, total_steps: int, duration: str) -> None:
    """
    在任务完成时添加结束标记到LLM日志文件，并生成两种格式化版本

    Args:
        llm_log_path: LLM日志文件路径
        total_steps: 总步骤数
        duration: 持续时间字符串
    """
    from datetime import datetime, timezone
    from .format_utils import format_llm_calls_log, format_complete_log

    if not llm_log_path.exists():
        print(f"⚠️  LLM日志文件不存在，跳过结束标记: {llm_log_path}")
        return

    # 添加结束标记
    end_time = datetime.now(timezone.utc)
    end_msg = f"\n=== Completed LLM logging at {end_time.strftime('%H:%M:%S')} UTC ===\n"
    end_msg += f"=== Total steps: {total_steps} | Duration: {duration} ===\n"
    end_msg += f"=== End of LLM calls log ===\n"

    # 追加结束信息
    with open(llm_log_path, 'a', encoding='utf-8') as f:
        f.write(end_msg)

    print(f"✅ LLM调用日志已完成: {llm_log_path}")

    # 生成格式化版本
    try:
        print("🔄 生成LLM调用格式化文件...")
        llm_formatted_path = format_llm_calls_log(llm_log_path)
        print(f"✅ LLM调用格式化完成: {llm_formatted_path}")

        print("🔄 生成完整日志格式化文件...")
        complete_formatted_path = format_complete_log(llm_log_path)
        print(f"✅ 完整日志格式化完成: {complete_formatted_path}")

        print(f"📄 现在你可以对比两个文件:")
        print(f"   - LLM调用专用: {llm_formatted_path}")
        print(f"   - 完整日志: {complete_formatted_path}")

    except Exception as e:
        print(f"⚠️  格式化过程中出错: {e}")
        print("原始日志文件仍然可用，可以稍后手动格式化")
