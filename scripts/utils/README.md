# Utils Package for Magnetic-One Test Script (M1)

这个包包含了用于 Magnetic-One 测试脚本的实用工具函数，已重构为模块化结构。

## 📁 包结构

```
scripts/utils/
├── __init__.py          # 包初始化文件，提供向后兼容的导入
├── logging_utils.py     # LLM日志记录相关功能
├── format_utils.py      # 日志格式化和处理功能
└── README.md           # 本文档
```

## 🔧 模块说明

### `logging_utils.py`
包含LLM日志记录的设置和完成功能：
- `setup_llm_logging()` - 设置LLM调用日志记录
- `finalize_llm_logging()` - 完成日志记录并生成格式化文件

### `format_utils.py`
包含日志格式化和处理功能：
- `format_llm_calls_log()` - 格式化LLM调用日志为JSON
- `format_complete_log()` - 格式化完整日志为JSON
- 内部辅助函数：
  - `_get_format_from_endpoint_type()` - 根据endpoint_type确定格式
  - `_extract_endpoint_type_from_filename()` - 从文件名提取endpoint_type
  - `_format_ollama_messages()` / `_format_openai_messages()` - 格式化消息
  - `_format_ollama_response()` / `_format_openai_response()` - 格式化响应
  - `_extract_timestamp_ollama()` / `_extract_timestamp_openai()` - 提取时间戳

## 📦 导入方式

### 1. 推荐方式 - 从包根导入（向后兼容）
```python
from utils import setup_llm_logging, finalize_llm_logging
from utils import format_llm_calls_log, format_complete_log
```

### 2. 具体模块导入
```python
from utils.logging_utils import setup_llm_logging, finalize_llm_logging
from utils.format_utils import format_llm_calls_log, format_complete_log
```

### 3. 内部函数导入（高级用法）
```python
from utils.format_utils import (
    _get_format_from_endpoint_type,
    _extract_endpoint_type_from_filename,
    _format_ollama_messages,
    _format_openai_messages
)
```

## 🔄 迁移指南

### 原有代码
```python
from utils import setup_llm_logging, finalize_llm_logging
from utils import format_llm_calls_log, format_complete_log
```

### 新代码（无需修改）
```python
from utils import setup_llm_logging, finalize_llm_logging
from utils import format_llm_calls_log, format_complete_log
```

**✅ 向后兼容：** 现有的导入语句无需修改，包的 `__init__.py` 文件确保了向后兼容性。

## 🎯 支持的格式

### Ollama格式
- 自动检测：文件名包含 `ollama`
- 消息字段：`thinking`, `images`, `tool_calls`
- 响应字段：`created_at`, `done`, `message`, `total_duration` 等

### OpenAI/CloudGPT格式
- 自动检测：文件名包含 `openai` 或 `cloudgpt`
- 消息字段：`name`
- 响应字段：`choices`, `usage`, `id`, `object` 等

## 📝 使用示例

### 基本用法
```python
from pathlib import Path
from utils import format_llm_calls_log

# 格式化日志文件
log_path = Path("scenario_1_ollama_llama3.1_llm_calls.log")
formatted_path = format_llm_calls_log(log_path)
print(f"格式化完成: {formatted_path}")
```

### 高级用法
```python
from utils.format_utils import _extract_endpoint_type_from_filename, _get_format_from_endpoint_type

# 手动检测格式
log_path = Path("scenario_1_cloudgpt_gpt4_llm_calls.log")
endpoint_type = _extract_endpoint_type_from_filename(log_path)  # "cloudgpt"
format_type = _get_format_from_endpoint_type(endpoint_type)     # "openai"
```

## 🔍 测试

运行测试脚本验证功能：
```bash
cd scripts
python3 debug_format.py
```

## 📋 需要修改导入的文件

以下文件已自动更新导入方式：
- ✅ `scripts/run_m1_test.py`
- ✅ `scripts/debug_format.py`

其他项目文件无需修改，因为包提供了向后兼容的导入接口。
