#!/usr/bin/env python3
"""
Utils Package for Magnetic-One Test Script (M1)
===============================================

This package contains utility functions for logging and data processing used by the Magnetic-One test script.

Modules:
    - logging_utils: LLM logging setup and finalization functions
    - format_utils: Log formatting and processing functions
"""

# Import main functions for backward compatibility
from .logging_utils import setup_llm_logging, finalize_llm_logging
from .format_utils import format_llm_calls_log, format_complete_log

# Export all public functions
__all__ = [
    'setup_llm_logging',
    'finalize_llm_logging', 
    'format_llm_calls_log',
    'format_complete_log'
]
