{
    // 使用 IntelliSense 了解相关属性。
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Llama",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "justMyCode": false,
            "python": "/home/<USER>/.conda/envs/m1/bin/python",
            "console": "integratedTerminal",
            "cwd": "${fileDirname}",
            "args": [
                // "--scenario", "1",
            ],
        },
        {
            "name": "Python: Async Debug",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "justMyCode": false,
            "python": "/home/<USER>/.conda/envs/m1/bin/python",
            "console": "integratedTerminal",
            "cwd": "${fileDirname}",
            "args": [
                "--scenario", "1",
                "--endpoint_type", "ollama",
                "--model", "llama3.1"
            ],
            "subProcess": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}/src/autogen-agentchat/src:${workspaceFolder}/src/autogen-ext/src:${workspaceFolder}/src/pyautogen"
            }
        },
        {
            "name": "Python Debugger: Current File with Arguments",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "python": "/home/<USER>/.conda/envs/m1/bin/python",
            "console": "integratedTerminal",
            "args": "${command:pickArgs}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}/src/autogen-agentchat/src:${workspaceFolder}/src/autogen-ext/src:${workspaceFolder}/src/pyautogen"
            }
        }
    ]
}