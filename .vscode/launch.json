{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Llama",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "justMyCode": false,
            "python": "/mnt/miniconda3/envs/m1/bin/python",
            "console": "integratedTerminal",
            "cwd": "${fileDirname}",
            "args": [
                "--scenario", "1",
            ]
        },
        {
            "name": "Python: Async Debug",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "justMyCode": false,
            "python": "/mnt/miniconda3/envs/m1/bin/python",
            "console": "integratedTerminal",
            "cwd": "${fileDirname}",
            "args": [
                "--scenario", "1",
            ],
            "asyncio": true  // 关键：启用异步调试
        },
        {
            // Use IntelliSense to learn about possible attributes.
            // Hover to view descriptions of existing attributes.
            // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
            "version": "0.2.0",
            "configurations": [
            
                {
                    "name": "Python Debugger: Current File with Arguments",
                    "type": "debugpy",
                    "request": "launch",
                    "program": "${file}",
                    "console": "integratedTerminal",
                    "args": "${command:pickArgs}"
                }
            ]
        }
    ]
}